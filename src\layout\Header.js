import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';

const Header = () => {
  const [showCatalogDropdown, setShowCatalogDropdown] = useState(false);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowCatalogDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const headerStyle = {
    backgroundColor: '#CDFF9A',
    padding: '15px 50px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'relative',
    zIndex: 1000
  };

  const logoStyle = {
    fontSize: '24px',
    fontWeight: 'bold',
    color: '#000000',
    textDecoration: 'none',
    display: 'flex',
    alignItems: 'center'
  };

  const logoTextStyle = {
    display: 'flex',
    flexDirection: 'column',
    lineHeight: '1'
  };

  const navStyle = {
    display: 'flex',
    listStyle: 'none',
    margin: 0,
    padding: 0,
    gap: '30px'
  };

  const navLinkStyle = {
    textDecoration: 'none',
    color: '#000000',
    fontSize: '14px',
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: '1px',
    transition: 'color 0.3s ease'
  };

  const navLinkHoverStyle = {
    color: '#ffffff'
  };

  const iconsStyle = {
    display: 'flex',
    gap: '15px',
    alignItems: 'center'
  };

  const iconStyle = {
    width: '20px',
    height: '20px',
    cursor: 'pointer',
    transition: 'transform 0.3s ease'
  };

  const dropdownContainerStyle = {
    position: 'relative',
    display: 'inline-block'
  };

  const dropdownMenuStyle = {
    position: 'absolute',
    top: '100%',
    left: '0',
    backgroundColor: '#ffffff',
    minWidth: '800px',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    zIndex: 1000,
    padding: '30px',
    display: showCatalogDropdown ? 'block' : 'none',
    marginTop: '15px'
  };

  const dropdownContentStyle = {
    display: 'flex',
    gap: '40px'
  };

  const categoryColumnStyle = {
    flex: 1
  };

  const categoryTitleStyle = {
    fontSize: '14px',
    fontWeight: 'bold',
    color: '#333',
    marginBottom: '15px',
    textTransform: 'uppercase',
    letterSpacing: '1px'
  };

  const categoryItemsStyle = {
    display: 'grid',
    gridTemplateColumns: 'repeat(2, 1fr)',
    gap: '20px'
  };

  const categoryItemStyle = {
    textAlign: 'center',
    textDecoration: 'none',
    color: '#333',
    transition: 'transform 0.3s ease'
  };

  const categoryImageStyle = {
    width: '120px',
    height: '120px',
    borderRadius: '8px',
    marginBottom: '10px',
    objectFit: 'cover'
  };

  const categoryLabelStyle = {
    fontSize: '12px',
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: '0.5px'
  };

  return (
    <header style={headerStyle}>
      {/* Logo */}
      <Link to="/" style={logoStyle}>
        <div style={logoTextStyle}>
          <span>HA</span>
          <span>SPORT</span>
        </div>
      </Link>

      {/* Navigation */}
      <nav>
        <ul style={navStyle}>
          <li>
            <Link 
              to="/" 
              style={navLinkStyle}
              onMouseEnter={(e) => e.target.style.color = navLinkHoverStyle.color}
              onMouseLeave={(e) => e.target.style.color = navLinkStyle.color}
            >
              HOME
            </Link>
          </li>
          <li style={dropdownContainerStyle} ref={dropdownRef}>
            <div
              onMouseEnter={() => setShowCatalogDropdown(true)}
              onMouseLeave={() => setShowCatalogDropdown(false)}
            >
              <div
                style={{
                  ...navLinkStyle,
                  cursor: 'pointer',
                  userSelect: 'none'
                }}
                onClick={() => setShowCatalogDropdown(!showCatalogDropdown)}
                onMouseEnter={(e) => e.target.style.color = navLinkHoverStyle.color}
                onMouseLeave={(e) => e.target.style.color = navLinkStyle.color}
              >
                CATALOGS
              </div>

              {/* Dropdown Menu */}
              <div style={dropdownMenuStyle}>
                <div style={dropdownContentStyle}>
                  {/* Left Column - Categories */}
                  <div style={categoryColumnStyle}>
                    <div style={categoryTitleStyle}>Activity</div>
                    <div style={categoryTitleStyle}>Equipment</div>
                    <div style={categoryTitleStyle}>Women's</div>
                    <div style={categoryTitleStyle}>Men's</div>
                  </div>

                  {/* Right Column - Product Images */}
                  <div style={categoryItemsStyle}>
                    <Link to="/catalog/boots" style={categoryItemStyle}>
                      <img
                        src="https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=120&h=120&fit=crop&crop=center"
                        alt="Boots"
                        style={categoryImageStyle}
                      />
                      <div style={categoryLabelStyle}>Boots</div>
                    </Link>

                    <Link to="/catalog/helmets" style={categoryItemStyle}>
                      <img
                        src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=120&h=120&fit=crop&crop=center"
                        alt="Helmets"
                        style={categoryImageStyle}
                      />
                      <div style={categoryLabelStyle}>Helmets</div>
                    </Link>

                    <Link to="/catalog/goggles" style={categoryItemStyle}>
                      <img
                        src="https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=120&h=120&fit=crop&crop=center"
                        alt="Goggles"
                        style={categoryImageStyle}
                      />
                      <div style={categoryLabelStyle}>Goggles</div>
                    </Link>

                    <Link to="/catalog/binding" style={categoryItemStyle}>
                      <img
                        src="https://images.unsplash.com/photo-1551524164-6cf2ac531fb4?w=120&h=120&fit=crop&crop=center"
                        alt="Binding"
                        style={categoryImageStyle}
                      />
                      <div style={categoryLabelStyle}>Binding</div>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </li>
          <li>
            <Link 
              to="/blog" 
              style={navLinkStyle}
              onMouseEnter={(e) => e.target.style.color = navLinkHoverStyle.color}
              onMouseLeave={(e) => e.target.style.color = navLinkStyle.color}
            >
              BLOG
            </Link>
          </li>
          <li>
            <Link 
              to="/shop" 
              style={navLinkStyle}
              onMouseEnter={(e) => e.target.style.color = navLinkHoverStyle.color}
              onMouseLeave={(e) => e.target.style.color = navLinkStyle.color}
            >
              SHOP
            </Link>
          </li>
          <li>
            <Link 
              to="/contact" 
              style={navLinkStyle}
              onMouseEnter={(e) => e.target.style.color = navLinkHoverStyle.color}
              onMouseLeave={(e) => e.target.style.color = navLinkStyle.color}
            >
              CONTACT US
            </Link>
          </li>
        </ul>
      </nav>

      {/* Icons */}
      <div style={iconsStyle}>
        {/* Search Icon */}
        <svg style={iconStyle} viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="11" cy="11" r="8"></circle>
          <path d="m21 21-4.35-4.35"></path>
        </svg>
        
        {/* Heart Icon */}
        <svg style={iconStyle} viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
        </svg>
        
        {/* User Icon */}
        <svg style={iconStyle} viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
        
        {/* Cart Icon */}
        <svg style={iconStyle} viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="9" cy="21" r="1"></circle>
          <circle cx="20" cy="21" r="1"></circle>
          <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
        </svg>
      </div>
    </header>
  );
};

export default Header;
